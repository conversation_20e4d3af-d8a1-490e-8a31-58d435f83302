<!--- Script 1:--Building/Building Permit/Commercial/New (Active) --->
var V_GROUP= "Building";
var V_TYPE = "Building Permit";
var V_SUBTYPE = "Commercial";
var V_CATEGORY = "New";
var V_DESC = "Create a record with active record type";

var checkResult = aa.cap.checkAppTypeStatus(V_GROUP,V_TYPE,V_SUBTYPE,V_CATEGORY);
if(checkResult.getSuccess())
{
    if("A" == checkResult.getOutput())
    {
         var capIDModel = aa.cap.createApp(V_GROUP,V_TYPE,V_SUBTYPE,V_CATEGORY,V_DESC); //create application that record type is active only. 
         aa.print("Create a record with record type active successfully! "+ capIDModel.getOutput());
    }
    else
    {
         var capIDModel = aa.cap.createAppRegardlessAppTypeStatus(V_GROUP,V_TYPE,V_SUBTYPE,V_CATEGORY,V_DESC);  //create application record regardless of record type status. 
         aa.print("Create a record with record type inactive successfully! "+ capIDModel.getOutput());
    }

    
}
else
{
  aa.print("fail");
  aa.print("ERROR: " + checkResult.getErrorMessage()); 
}



<!--- Script 2:--Building/Building Permit/Commercial/Club (Inactive) --->

var V_GROUP= "Building";
var V_TYPE = "Building Permit";
var V_SUBTYPE = "Commercial";
var V_CATEGORY = "Club";
var V_DESC = "Create a record with inactive record type";

var checkResult = aa.cap.checkAppTypeStatus(V_GROUP,V_TYPE,V_SUBTYPE,V_CATEGORY);
if(checkResult.getSuccess())
{
    if("A" == checkResult.getOutput())
    {
         var capIDModel = aa.cap.createApp(V_GROUP,V_TYPE,V_SUBTYPE,V_CATEGORY,V_DESC); //create application that record type is active only. 
         aa.print("Create a record with record type active successfully! "+ capIDModel.getOutput());
    }
    else
    {
         var capIDModel = aa.cap.createAppRegardlessAppTypeStatus(V_GROUP,V_TYPE,V_SUBTYPE,V_CATEGORY,V_DESC);  //create application record regardless of record type status. 
         aa.print("Create a record with record type inactive successfully! "+ capIDModel.getOutput());
    }

    
}
else
{
  aa.print("fail");
  aa.print("ERROR: " + checkResult.getErrorMessage()); 
}
