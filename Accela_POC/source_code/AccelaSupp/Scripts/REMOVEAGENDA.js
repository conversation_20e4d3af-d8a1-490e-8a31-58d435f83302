//Description:
//This sample script is used to remove all agendas from a meeting.  (Cancel a meeting for a record.)
//Event Name:
//-----------------------------------------------------------------------------------------------------------
var meetingID= 6266;
var meetingGroupID= 12;

var capIDListResult = aa.meeting.getMeetingAgendaItems(meetingGroupID, meetingID);

if(capIDListResult.getSuccess())
{
	var capIDList = capIDListResult.getOutput();
	for (var i=0; i < capIDList.size(); i++)
	{
		var removeResult = aa.meeting.removeAgendaFromMeeting(meetingGroupID, meetingID, capIDList.get(i));
		if(removeResult.getSuccess())
		{
			aa.env.setValue("ScriptReturnMessage", "Meetings are cancelled successfully!");
		}
	}
}

