var DocumentID= aa.env.getValue("DocumentID");
var DocumentModel =aa.env.getValue("DocumentModel");
var associationModel= aa.env.getValue("DocumentEntityAssociationModel");
var taskItemModel = null;
var mailFrom = "<EMAIL>";
var mailCC = "<EMAIL>";

if(associationModel != null && associationModel != "")
{
	var processId = associationModel.getEntityID2()
	var stepNbr = associationModel.getEntityID3();
	var result = aa.document.getTaskItemModel();
	if (result.getSuccess())
	{
		taskItemModel = result.getOutput();
		taskItemModel.setProcessID(processId);
		taskItemModel.setStepNumber(stepNbr);
	}
}

noticeReviewers();



function noticeReviewers()
{
	var result = aa.document.getRelatedReviewers(DocumentID, taskItemModel);
	if(result.getSuccess())
	{
		var reviewers = result.getOutput();
		// send email to reviewers for every document
		if (reviewers != null && reviewers.size() > 0)
		{
			for (var j = 0; j < reviewers.size(); j++)
			{
				var userId = reviewers.get(j).getEntityID1();
				var subject = "Publish document comments successfully, please help to review the document : " + DocumentModel.getName();
				var emailContent = "This is an automated email notification.  Please do not reply to this email.";
	
				if(userId != null && userId != "")
				{		
					// use user id to get the user info and get the user email.
					var reviewerResult = aa.people.getSysUserByID(userId);
					if(reviewerResult.getSuccess())
					{
						reviewer = reviewerResult.getOutput();
						var emailTo = reviewer.getEmail();
						if(emailTo != null)
						{								
							sendEmail(emailTo,subject,emailContent);
						}
					}
				}
			}
		}
		aa.env.setValue("ScriptReturnMessage", "Notice reviewers successfully.");
		aa.env.setValue("ScriptReturnCode", "0");
	}
	else
	{
		aa.print("ERROR: Failed to get associate reviewers: " + result.getErrorMessage());
	}
}


/*
 * Send email.
 */
function sendEmail(userEmailTo,subject,emailContent)
{
	var result = aa.sendMail(mailFrom, userEmailTo, mailCC, subject, emailContent);
	if(result.getSuccess())
	{
		aa.log("Send email successfully!");
		return true;
	}
	else
	{
		aa.log("Fail to send mail.");
		return false;
	}
}

 
  
