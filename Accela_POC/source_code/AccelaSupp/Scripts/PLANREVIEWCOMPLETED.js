/*------------------------------------------------------------------------------------------------------/
 * This is a sample script for update work flow task, auto active next work flow task 
 * and assign specify user to next active work flow task and send email to contacts on the record
 * when all document review tasks completed (approved).
 * Note: this sample only do above actions when the workflow task is "Plan Review".
 /------------------------------------------------------------------------------------------------------*/


/*------------------------------------------------------------------------------------------------------/
 * begin define event parameters
 /------------------------------------------------------------------------------------------------------*/
//Get document review model from event parameter.
var documentReviewModel = aa.env.getValue("DocumentReviewModel");

//Get document review task information from the review model.
var servProvCode = documentReviewModel.getServiceProviderCode();
var documentID = documentReviewModel.getDocumentID();
var processID = documentReviewModel.getEntityID2();
var stepNum = documentReviewModel.getEntityID3();
// Get Cap ID Model
var capID = getCapID(documentReviewModel.getID1(),
		documentReviewModel.getID2(), documentReviewModel.getID3());
// define a workflow task name that the document review tasks associated with.
var planReview = "Plan Review";

var callerId = aa.getAuditID();

// Notification Template name
var templateName = "PlanReviewCompleted";
// email address that use for sending email from
var mailFrom ="<EMAIL>";
var mailCC="<EMAIL>";

var message = "";
var isPlanReview = false;

if (processID != null && processID != "") {
	//current workflow task by current plan review task
	var currentWorkflowTask = getWorkflowByDocReviewTask(capID, stepNum, processID);
	isPlanReview = isPlanReviewWorkflow(currentWorkflowTask);
	doActionOnTheWorkflow(currentWorkflowTask);
}


/*------------------------------------------------------------------------------------------------------/
 * do some action (update current workflow task, active next, assign user to next, send email to contacts)
 * base on plan review workflow and current active workflow task is plan review
 /------------------------------------------------------------------------------------------------------*/
function doActionOnTheWorkflow(fTask) {
	
	if (!isCurrentWorkflowTaskIsPlanReview(capID)) {
		aa.print("Current work flow task is not plan review, so will not go on.");
		return;
	}
	
	var stepnumber = fTask.getStepNumber();
	var processId = fTask.getProcessID();
	if (isPlanReview) {
		var planReviewCompleted = isPlanReviewTaskCompleted(servProvCode, capID,
				stepnumber, processId);
		// 1. if all review task completed, then update current workflow task 
		if (planReviewCompleted) {
			aa.print("Update the task: " + fTask.getTaskDescription());
//			fTask.setDisposition("Plat/Plan Complete");
			fTask.setDispositionComment("Update the task when all document review completed by EMSE.");
			aa.workflow.editTask(fTask, callerId);
		}
		// 2. if all review task completed, then activate next workflow task
		if (planReviewCompleted) {
			
			aa.print("Active the next task.");
			var disposition = "Plat/Plan Complete";
			var dispositionNote="disposition Note";
			var dispositionComment="disposition Comment";
			var dispositionDate = aa.date.getCurrentDate();
			var systemUserObj = aa.person.getUser(callerId).getOutput();
			aa.workflow.handleDisposition(capID, stepnumber, processId,
					disposition, dispositionDate, dispositionNote,
					dispositionComment, systemUserObj, "Y");
			
			// 3. if all review task completed, get the active task and then assign some one to it
			aa.print("Assign task:");
			var scriptTasks = getActiveWorkflowTasks(capID);
			if (scriptTasks != null) {
				var size = scriptTasks.length;
				for (var i = 0; i < size; i++) {
					var wftask = scriptTasks[i].getTaskItem();
					if (wftask != null) {
						aa.print(wftask.getTaskDescription());
						var sysUserResult = aa.people.getSysUserByID("ADMIN");
						if (sysUserResult.getSuccess()) {
							var assignee = sysUserResult.getOutput();
							wftask.setAssignedUser(assignee);
							aa.workflow.assignTask(wftask);
						}
					}
				}
			}
		}
		
		// 4. send email to contacts if all document review task completed
		if (planReviewCompleted) {
			sendEmail2Contacts(capID);
		}
		
		//5. alert message
		if (planReviewCompleted) {
			var subject4Alert = "Plan review completed", content = "Your plans have been reviewed completely!";
			aa.alert.createAlertMessage(subject4Alert, content, callerId);
		}
	}
}


/*------------------------------------------------------------------------------------------------------/
 * check if all plan review tasks completed
 /------------------------------------------------------------------------------------------------------*/
function isPlanReviewTaskCompleted(servProvCode, capID, stepNum, processID) {
	var completed = true;
	var taskItemModel = aa.document.getTaskItemModel().getOutput();
	taskItemModel.setServiceProviderCode(servProvCode);
	taskItemModel.setProcessID(processID);
	taskItemModel.setStepNumber(stepNum);

	// get all review Tasks under the work flow task
	var resutl = aa.document.getRelatedReviewers(null, taskItemModel);
	
	//check the review task status are approved
	if (resutl.getSuccess()) {
		var reviewTasks = resutl.getOutput();
		if (reviewTasks != null && reviewTasks.size() > 0) {
			var len = reviewTasks.size();
			for (var j = 0; j < len; j++) {
				if (!isUnderCurrentCap( reviewTasks.get(j), capID)) {
					continue;
				}
				var status = reviewTasks.get(j).getStatus();
				if (!"Approved".equalsIgnoreCase(status)) {
					completed = false;
					break;
				}
			}
		}
	}
	return completed;
}

//check is under the same cap
function isUnderCurrentCap(docReviewTask, cap) {
	if (docReviewTask != null && cap != null) {
		var id1 = docReviewTask.getID1();
		var id2 = docReviewTask.getID2();
		var id3 = docReviewTask.getID3();

		var cap_id1 = cap.getID1();
		var cap_id2 = cap.getID2();
		var cap_id3 = cap.getID3();

		if (id1.equals(cap_id1) && id2.equals(cap_id2) && id3.equals(cap_id3)) {
			return true;
		}
	}
	return false;
}

/*------------------------------------------------------------------------------------------------------/
 * Get workflow task by current plan review task
 /------------------------------------------------------------------------------------------------------*/
function getWorkflowByDocReviewTask(capID, stepNum, processID) {
	var taskItemScriptModel;
	var result = aa.workflow.getTask(capID, stepNum, processID);
	if (result.getSuccess()) {
		taskItemScriptModel = result.getOutput();
		if (taskItemScriptModel == null) {
			aa.print("ERROR: Failed to get workflow task with CAPID(" + capID
					+ ") for review");
		} else {
			return taskItemScriptModel.getTaskItem();
		}
	} else {
		aa.print("ERROR: Failed to get workflow task(" + capID
				+ ") for review: " + result.getErrorMessage());
	}
	return null;
}


/*------------------------------------------------------------------------------------------------------/
 * check if the current active workflow task is "plan review"
 /------------------------------------------------------------------------------------------------------*/
function isCurrentWorkflowTaskIsPlanReview(capID) {
	var activeTasks = getActiveWorkflowTasks(capID);
	if (activeTasks != null) {
		var size = activeTasks.length;
		for (var i = 0; i < size; i++) {
			var actTask = activeTasks[i].getTaskItem();
			if (actTask != null) {
				aa.print("Current work flow task is: " + actTask.getTaskDescription());
				if (planReview.equalsIgnoreCase(actTask.getTaskDescription())) {
					return true;
					break;
				}
			}
		}
	}
	return false;
}

/*------------------------------------------------------------------------------------------------------/
 * check if the workflow name is "plan review"
 /------------------------------------------------------------------------------------------------------*/
function isPlanReviewWorkflow(workflowTask) {
	if (workflowTask != null) {
		logMessage("Workflow task:  "
				+ workflowTask.getTaskDescription());
		// 2. Check to see if the work flow is plan review (just sample)
		if (planReview.equalsIgnoreCase(workflowTask.getTaskDescription())) {
			return true;
		} else {
			aa.print("The work flow "
					+ workflowTask.getTaskDescription()
					+ " is not Plan Review.");
		}
	}
	return false;
}

/*------------------------------------------------------------------------------------------------------/
 * get active and not completed work flow tasks under cap
 /------------------------------------------------------------------------------------------------------*/
function getActiveWorkflowTasks(capID)
{
	var taskResult = aa.workflow.getTaskItems(capID, null, null,"N", null, "Y");
	if (taskResult.getSuccess()) {
		var scriptTasks = taskResult.getOutput();
		if (scriptTasks != null && scriptTasks.length > 0) {
			return scriptTasks;
		}
	}
	return null;
}

function getCapID(id1, id2, id3) {
	var s_capResult = aa.cap.getCapID(id1, id2, id3);
	if (s_capResult.getSuccess()) {
		return s_capResult.getOutput();
	} else {
		aa.print("ERROR: Failed to get capId: "
						+ s_capResult.getErrorMessage());
		return null;
	}
}

/*------------------------------------------------------------------------------------------------------/
 * Get full name from PeopleModel
 /------------------------------------------------------------------------------------------------------*/
function getPeopleFullName(people) {
	var emptyString = /^\s*$/;
	var result = '';
	if (people != null) {
		result = people.getFullName();
		if (!result || emptyString.test(result)) {
			var firstName = people.getFirstName();
			var middleName = people.getMiddleName();
			var lastName = people.getLastName();

			if (firstName && !emptyString.test(firstName)) {
				result = firstName;
			}
			if (middleName && !emptyString.test(middleName)) {
				if (result && !emptyString.test(result)) {
					result += ' ';
				}
				result += middleName;
			}
			if (lastName && !emptyString.test(lastName)) {
				if (result && !emptyString.test(result)) {
					result += ' ';
				}
				result += lastName;
			}
		}
	}
	return result;
}

function getCapScriptModel(capID)
{
    return aa.cap.createCapIDScriptModel(capID.getID1(), capID.getID2(), capID.getID3());
}

/*------------------------------------------------------------------------------------------------------/
| send email to contacts on the record 
/------------------------------------------------------------------------------------------------------*/
function sendEmail2Contacts(capID)
{
	var capContactResult = aa.people.getCapContactByCapID(capID);
	if (capContactResult.getSuccess())
	 {
		var emailTo = '';
		var contacts = capContactResult.getOutput();
		for ( var contactIdx in contacts) {

			emailTo = contacts[contactIdx].getCapContactModel().getEmail();
			firstName = contacts[contactIdx].getCapContactModel().getFirstName();
			middleName = contacts[contactIdx].getCapContactModel().getMiddleName();
			lastName = contacts[contactIdx].getCapContactModel().getLastName();
			var fullName = getPeopleFullName(contacts[contactIdx]
					.getCapContactModel());
			var params = buildParams4EmailNotification(fullName, capID.getCustomID());
			if (emailTo != null && emailTo != '') {
				sendNotification(emailTo, templateName, params, null);
			}
		}
	}
}

function buildParams4EmailNotification(applicant, altID)
{
	var emailParameters = aa.util.newHashtable();
	addParameter(emailParameters, "$$applicant$$",applicant);
	addParameter(emailParameters, "$$AltID$$",altID);
	addParameter(emailParameters, "$$subject$$","Your plan reivew have been completed");
	addParameter(emailParameters, "$$callerID$$",callerId);
	return emailParameters;
}

function addParameter(pamaremeters, key, value)
{
	if (key != null) {
		if (value == null) {
			value = "";
		}
		pamaremeters.put(key, value);
	}
}

/*------------------------------------------------------------------------------------------------------/
 * Send notification by email
 /------------------------------------------------------------------------------------------------------*/
function sendNotification(userEmailTo, templateName, params, reportFile) {
	var result = null;
	result = aa.document.sendEmailAndSaveAsDocument(mailFrom, userEmailTo,
			mailCC, templateName, params, getCapScriptModel(capID), reportFile);
	if (result.getSuccess()) {
		aa.log("Send email " + userEmailTo +" successfully!");
		return true;
	} else {
		aa.log("Fail to send mail.");
		return false;
	}
}

function logMessage(msg) {
	message += msg + '<br>';
}

aa.env.setValue("ScriptReturnMessage", message);
aa.env.setValue("ScriptReturnCode", "0");
