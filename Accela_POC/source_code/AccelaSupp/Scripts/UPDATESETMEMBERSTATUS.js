// Update a SetMember Status for a Record in SetMember List.

var capIDModel= aa.cap.getCapID("12CAP","00000","00059").getOutput();
var setUpdateScript = aa.set.getSetDetailsScriptModel().getOutput();
setUpdateScript.setSetID("12-CAP-00031");          //Set ID
setUpdateScript.setID1(capIDModel.getID1());
setUpdateScript.setID2(capIDModel.getID2());
setUpdateScript.setID3(capIDModel.getID3());
setUpdateScript.setSetMemberStatus("License to Goto");   // Set Member Status
setUpdateScript.setSetMemberStatusDate(aa.date.getCurrentDate());   //Set Member Status Date
setUpdateScript.setServiceProviderCode("NYC");

var updateScript = aa.set.updateSetMemberStatus(setUpdateScript);
if (updateScript.getSuccess())
{
	aa.print("updateSetMemberStatus Successfully");
}
else
{
	aa.print("updateSetMemberStatus Failed!");
	aa.print(updateScript.getErrorMessage());		
} 
