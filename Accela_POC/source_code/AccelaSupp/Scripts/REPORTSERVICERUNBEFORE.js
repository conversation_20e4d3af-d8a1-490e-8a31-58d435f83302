aa.print("ReportServiceRunBeforeScript param:");
aa.print("serviceProviderCode=" + aa.env.getValue("serviceProviderCode"));
aa.print("reportId=" + aa.env.getValue("reportId"));
aa.print("portletName=" + aa.env.getValue("portletName"));
aa.print("callerId=" + aa.env.getValue("callerId"));
aa.print("module=" + aa.env.getValue("module"));

aa.print("PermitId1=" + aa.env.getValue("PermitId1"));
aa.print("PermitId2=" + aa.env.getValue("PermitId2"));
aa.print("PermitId3=" + aa.env.getValue("PermitId3"));


var capID = getCapID();

var taskStatusDesc = "Ready";
var taskDescription = "Issuance";
main(capID,taskStatusDesc);
 
function main(capID,taskStatusDesc)
{
	
	var taskItem = getTaskItem(capID, taskDescription);
	var scriptTask = getTaskItemScriptModel();
	scriptTask.setCapID(capID);
	scriptTask.setTaskItem(taskItem);

	var taskStatusScriptModel = getTaskStatus(scriptTask,taskStatusDesc)

  	if (taskStatusScriptModel == null)
	{
		//stop report run
		aa.env.setValue("ScriptReturnCode","1");
		aa.env.setValue("ScriptReturnMessage","The report is stopped by EMSE.");
	}
	else
	{
		//continue report run
		aa.env.setValue("ScriptReturnCode","0");
		aa.env.setValue("ScriptReturnMessage","The report continue run by EMSE.");
	}
}


function getTaskStatus(scriptTask,taskStatusDesc)
{
	var taskStatusScriptModel= null;
	var result = aa.workflow.getTask(capID, taskDescription);
	if(result.getSuccess())
	{
		taskStatusScriptModel= result.getOutput();
		if (taskItemScriptModel == null)
		{
			aa.print("ERROR: Failed to get workflow task status with CAPID(" + capID + ")");
		}
	}  
	else 
	{
		aa.print("ERROR: Failed to get workflow task status(" + capID + ") for review: " + result.getErrorMessage());
	}

	return taskStatusScriptModel;
}


function getTaskItem(capID,taskDescription)
{
	var taskItemScriptModel = null;
	var result = aa.workflow.getTask(capID, taskDescription);
	if(result.getSuccess())
	{
		taskItemScriptModel = result.getOutput();
		if (taskItemScriptModel == null)
		{
			aa.print("ERROR: Failed to get workflow task with CAPID(" + capID + ")");
		}
	}  
	else 
	{
		aa.print("ERROR: Failed to get workflow task(" + capID + ") for review: " + result.getErrorMessage());
	}

	return taskItemScriptModel;
}

function getCapID()
{
	var id1 = aa.env.getValue("PermitId1");
	var id2 = aa.env.getValue("PermitId2");
	var id3 = aa.env.getValue("PermitId3");
	var capIDResult = aa.cap.getCapID(id1, id2, id3);
	if(capIDResult.getSuccess())
	{
		return capIDResult.getOutput();
	}
	return null;
}

function getTaskItemScriptModel()
{	
	var taskItemScriptResult = aa.workflow.getTaskItemScriptModel();
	if(taskItemScriptResult.getSuccess())
	{
		return taskItemScriptResult.getOutput();
	}
	return null;
}
