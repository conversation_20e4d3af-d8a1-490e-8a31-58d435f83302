/**
  Sync Cap contact from Reference contact.

*/
var peopleSearchModel = aa.people.getPeopleModel();
peopleSearchModel.setFirstName("paul");
peopleSearchModel.setLastName("wu");

var peopleScriptModelList = aa.people.getPeopleByPeopleModel(peopleSearchModel).getOutput();
var people = null;
if(peopleScriptModelList !=null)
{
    people = peopleScriptModelList[0].getPeopleModel();
}

var capID = aa.cap.getCapID("13CAP","00000","00259").getOutput();
if(capID != null)
{
	var capContactResult = aa.people.getCapContactByRefPeopleModel(capID,peopleSearchModel);
	var capContactList = capContactResult.getOutput();
	if(capContactList != null && people != null)
	{
    		var capContact = capContactList[0].getCapContactModel();
    		var syncResult = aa.people.syncCapContactFromReference(capContact,people);
    		if(syncResult.getSuccess())
    		{
       			aa.print("Cap contact synchronized successfully!");
    		}
    		else
    		{
       			aa.print("Cap contact synchronized. " + syncResult.getErrorMessage());
    		}
	}
}	


/**
   Sync Cap contact to Reference contact.

*/
var peopleSearchModel = aa.people.getPeopleModel();
peopleSearchModel.setFirstName("paul");
peopleSearchModel.setLastName("wu");

var peopleScriptModelList = aa.people.getPeopleByPeopleModel(peopleSearchModel).getOutput();
var people = null;
if(peopleScriptModelList !=null)
{
    people = peopleScriptModelList[0].getPeopleModel();
}

var capID = aa.cap.getCapID("13CAP","00000","00259").getOutput();
if(capID != null)
{
	var capContactResult = aa.people.getCapContactByRefPeopleModel(capID,peopleSearchModel);
	var capContactList = capContactResult.getOutput();
	if(capContactList != null && people != null)
	{
    		var capContact = capContactList[0].getCapContactModel();
    		var syncResult = aa.people.syncCapContactToReference(capContact,people);
    		if(syncResult.getSuccess())
    		{
       			aa.print("Cap contact synchronized successfully!");
    		}
    		else
    		{
       			aa.print("Cap contact synchronized failed. " + syncResult.getErrorMessage());
    		}
	}
}	
