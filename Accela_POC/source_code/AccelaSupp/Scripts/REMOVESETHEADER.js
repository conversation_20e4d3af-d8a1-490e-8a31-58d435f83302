// Sample Script1: Remove the Set by Set ID.

var remove = aa.set.removeSetHeader("12-SET-00028");
  if (remove.getSuccess())
  {
   aa.print("Remove Set successfully");
  }
  else
  {
   aa.print("Failed to remove the Set");
  }


// Sample Script2: remove the Set by Set ID after created it from EMSE.

var SetID = "EMSE SETID1217";
var SetTitle = "EMSE SET1217";
var SetStatusComment = "Create a Set by EMSE";
var SetStatus = " ";
var SetType = "Other";
var setscript = aa.set.getSetHeaderScriptModel().getOutput();
setscript.setSetID(SetID);
setscript.setSetTitle(SetTitle);
setscript.setSetStatusComment(SetStatusComment);
setscript.setSetStatus(SetStatus);
setscript.setRecordSetType(SetType);
setscript.setServiceProviderCode("SACRAMENTO");
setscript.setAuditDate(aa.date.getCurrentDate());
setscript.setAuditID("Admin");

NewSet = aa.set.createSetHeader(setscript);
if (NewSet.getSuccess())
{
 var set = aa.set.getSetHeaderScriptModel().getOutput();
 set.setSetID("EMSE SETID1217");
 var getSet = aa.set.getSetHeaderListByModel(set);
 if (getSet.getSuccess())
 {
  
//Delete Set by Set ID.
var remove = aa.set.removeSetHeader("EMSE SETID1217");
  if (remove.getSuccess())
  {
   aa.print("Remove Set successfully");
  }
  else
  {
   aa.print("Failed to remove the Set");
  }
 }
 else
 {
  aa.print("Failed to get Set List");
 }
}
else
{
 aa.print("Failed to create Set");
}
