

aa.print("--------------Meeting Field---------------");
aa.print("-------------------START--------");
var meetings = aa.env.getValue("MeetingModelList");
var events = aa.env.getValue("CalendarEventModelList");
aa.print("--------------MeetingModel-------------");
for(var i = 0; i < meetings.size(); i++)
{
	var meeting = meetings.get(i);
	aa.print(meeting.startDate);
	aa.print(meeting.meetingType);
	aa.print(meeting.meetingName);	
	aa.print(meeting.meetingBody);
	aa.print(meeting.meetingLocation);
	aa.print(meeting.meetingNoticeDate);
	aa.print(meeting.meetingStatus);
	aa.print(meeting.meetingStatusType);
	aa.print(meeting.meetingComment);
	aa.print(meeting.emailNotification);
}
aa.print("--------------CalendarEventModel-------------");
for(var i = 0; i < events.size(); i++)
{
	var event = events.get(i);
	aa.print(event.startDate);
	aa.print(event.endDate);
	aa.print(event.eventType);
	aa.print(event.eventName);	
	aa.print(event.hearingBody);
	aa.print(event.eventLocation);
	aa.print(event.eventNoticeDate);
	aa.print(event.eventStatus);
	aa.print(event.eventStatusType);
	aa.print(event.comment);
	aa.print(event.emailNotification);
	
	
}

aa.print("--------------END---------");
aa.print("Deactivate Meeting End:");

aa.env.setValue("ScriptReturnCode","0");
aa.env.setValue("ScriptReturnMessage", "successful");

/**
 * Add one MeetingCalendar.Last all day.
 */
function addMeeting()
{
	aa.print("--------------this addMeeting method-------------");
	var meetingName = "QATest" ;
	var meetingType = "WHAT" ;
	var meetingDate = "2012/10/10" ;
	var calendarID = "1106" ;
	var result = aa.meeting.addMeeting(calendarID,meetingName,meetingType,meetingDate);
	var meetingIdList = result.output ;
	for (var i=0;i<meetingIdList.size();i++)
	{
		aa.print(i + "ID:" + meetingIdList.get(i));
	}
}

/**
 * Get one Calendar through ID
 */
function getMeetingCalendar()
{
	aa.print("--------------this getMeetingCalendar method-------------");
	var result = aa.meeting.getMeetingCalendar("1106");
	aa.print("The GroupName:" + result.output.getMeetingGroupName());
	aa.print("The GroupType:" + result.output.getMeetingGroupType());
	aa.print("The GroupComment:" + result.output.getMeetingGroupComment());
}

/**
 * Get available Meetings
 */
function getAvailableMeetings()
{
	aa.print("--------------this getAvailableMeetings method-------------");
	var dateFrom = aa.date.parseDate('10/15/2012');
	var dateTo = aa.date.parseDate('10/17/2012');
	var meetingBody = "LOOK" ;
	var duration = 60 ;
	var calendarName = "Samuel" ;
	var dayOfWeek = null ;
	var location = "TEST_LOCATION" ;
	var result = aa.meeting.getAvailableMeetings(meetingBody,duration,calendarName,dateFrom,dateTo,dayOfWeek,location);
	aa.print("has count:" + result.output.length);
}

/**
 * Get MeetingCalendars through calendarName and calendarType
 */
function getMeetingCalendars()
{
	aa.print("--------------this getMeetingCalendars method-------------");
	var calendarName = "Samuel" ;
	var calendarType = "MEETING" ;
	var result = aa.meeting.getMeetingCalendars(calendarName,calendarType);
	var model = result.output[0];
	aa.print("The Meeting Name:" + model.getMeetingGroupName());
	result = aa.meeting.getMeetingCalendars();
	var model = result.output[0];
	aa.print("The Meeting Name:" + model.getMeetingGroupName());
}


/**
 * Delete one Meeting and template through calendarID and meetingID 
 */
function deleteMeetingByMeetingID()
{
	aa.print("--------------this deleteMeetingByMeetingID method-------------");
	var calendarID = "1706" ;
	var meetingID = "29494" ;
	var result = aa.meeting.deleteMeetingByMeetingID(calendarID,meetingID);
	aa.print("Delete Meeting :" + result.success);
}


/**
 * Get one Meeting  and template through calendarID and meetingID
 */
function getMeetingByMeetingID()
{
	aa.print("--------------this getMeetingByMeetingID method-------------");
	//var calendarID = "1106" ;
	//var meetingID = "22449" ;
        var calendarID = "1706" ;
	var meetingID = "29494" ;
	var result = aa.meeting.getMeetingByMeetingID(calendarID,meetingID);
	aa.print("Meeting Name:" + result.output.getMeetingName());

	var template =result.output.getTemplate();

        getTemplateForSimple(template);
	
}

/**
 * Get one Series of Meeting through meetingCalendarID, year and month.
 */
function getMeetingSeriesByCalendarID()
{
	aa.print("--------------this getMeetingSeriesByCalendarID method-------------");
	var meetingCalendarID = "1106" ;
	var year = 2012 ;
	var month = 10 ;
	var result = aa.meeting.getMeetingSeriesByCalendarID(meetingCalendarID,year,month);
	var meetings = result.output ;
	for (var i=0;i<meetings.length;i++)
	{
		var meeting = meetings[i];
		aa.print(i+"Meeting Name:" + meeting.getMeetingName());
	}
}
function getTemplateForSimple(template)
{
	var form = "Form";
	var table = "Table";
	if(!template.getTemplateForms)
	   return;
	var group =template.getTemplateForms().toArray()[0];
	var groupName=group.getGroupName();
	var subgroup = group.getSubgroups().toArray()[0];
	var subgroupName= subgroup.getSubgroupName();
	var field = subgroup.getFields().toArray()[0];
	var fieldName = field.getFieldName();

	aa.print("Demo Simple Template Case"); 
	aa.print("Field Name: "+ fieldName ); 
	aa.print("Field Value: "+ field.getDefaultValue()); 
}


function updateMeeting()
{
	aa.print("--------------this updateMeeting method-------------");
	var meetingCalendarID = "1106" ;
	var meetingID = "22449" ;
	var meetingName = "QaACEKKKK" ;
	var meetingType = "MEETING" ;
	var meetingDate = "10/16/2012" ;
	var result = aa.meeting.updateMeeting(meetingCalendarID,meetingID,meetingName,meetingType,meetingDate);
	aa.print("Update Meeting:" + result.success);
}

function scheduleMeeting()
{
	aa.print("--------------this scheduleMeeting method-------------");
	var capID = aa.cap.getCapID("12CAP","00000","002SQ");
	var meetingCalendarID = "1106" ;
	var meetingID = "22449" ;
	var duration = "60" ;
	var reason = "fly" ;
	var comments = "Human flight feasibility report." ;
	var result = aa.meeting.scheduleMeeting(capID.getOutput(),meetingCalendarID,meetingID,duration,reason,comments);
	aa.print("isSuccess:" + result.success);
}
