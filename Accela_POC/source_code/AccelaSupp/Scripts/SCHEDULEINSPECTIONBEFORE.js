var publicUserSeqNum= aa.env.getValue("userID");
var serviceProviderCode = aa.env.getValue("ServiceProviderCode");
var callerID = aa.env.getValue("callerID");
var capID = aa.env.getValue("capID");



var licenseList = aa.contractorLicense.getContrLicListByUserSeqNBR(publicUserSeqNum, serviceProviderCode).getOutput();


var capIDList = aa.cap.getCapsByLicenseProfessionalList(serviceProviderCode, callerID, licenseList).getOutput();
var flag = "N";
for (var i = 0; i<capIDList.size(); i++)
{
	if (capID.equals(capIDList.get(i)))
	{
		flag = "Y";
	}
}
if (flag == "N")
{
	aa.print(flag);
        aa.env.setValue("ScriptReturnCode","-1");
}
