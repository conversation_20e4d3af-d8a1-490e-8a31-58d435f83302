//Set parameters to Update relationship.
var contactEntityID = "3534";
var currentRole = "CEO";
var targetRole = "CTO";
var startDate = new Date("2012/02/03");
var endDate = new Date("2012/12/03");
var status = "A";

var result = aa.people.updateRefContactRelationship(contactEntityID, currentRole, targetRole, startDate, endDate, status);
if (result.getSuccess())
{
	aa.print("Update reference contact relationship successfully");
	aa.print(result.getOutput());
}
else
{
	aa.print("Update reference contact relationship failed");
	aa.print(result.getErrorMessage());	
}
