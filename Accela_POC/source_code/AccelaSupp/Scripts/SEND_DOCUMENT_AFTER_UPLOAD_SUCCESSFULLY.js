//Description:
//This sample script is used to send the document to all meeting attendees after uploading meeting attachment successfully.
//Event Name: DocumentUploadAfter
//-----------------------------------------------------------------------------------------------------------
var docList = aa.env.getValue("DocumentModelList");
if (aa.env.getValue("calendarID") != "" && aa.env.getValue("meetingID") != "")
{
	  var calendarID = parseInt(aa.env.getValue("calendarID"));
    var meetingID = parseInt(aa.env.getValue("meetingID"));
    
    aa.print("CalendarID:" + calendarID);
    aa.print("MeetingID:" + meetingID);
	
	var sendFlag = true;
	var maxAttachmentSize = aa.meeting.getAttachmentMaximumSize(); // unit: byte
	if (maxAttachmentSize && maxAttachmentSize.getSuccess())
	{
		var maxSize = maxAttachmentSize.getOutput();
		if (maxSize > 0)
		{
			if (!checkAttachmentSize(docList, maxSize))
			{
				aa.print("Failed");
				var maxSizeStr = maxSize/1024 >= 1024 ? (maxSize/(1024 * 1024)) + "MB" : maxSize/1024 + "KB";
				aa.env.setValue("ScriptReturnMessage", "Send Email document failed. The file size cannot be larger than " + maxSizeStr + ".");
				aa.env.setValue("ScriptReturnCode", "-1");
				sendFlag = false;
			}
		}
	}
    
	if (sendFlag)
	{
		var result = sendEmailForMeeting(calendarID, meetingID, "Test Email", "Hello World",  docList);
		
		if (result && result.getSuccess())
		{
			aa.print("Successful");
			aa.env.setValue("ScriptReturnMessage", "Send Email document successful");
			aa.env.setValue("ScriptReturnCode", "0");
		}
		else
		{
			aa.print("Failed");
			aa.env.setValue("ScriptReturnMessage", "Send Email document failed. " + result.getErrorMessage());
			aa.env.setValue("ScriptReturnCode", "-1");
		}
	}
}

/**
* check attachment size whether exceed E-mail server attachment limit size
*/
function checkAttachmentSize(docList, maxSize)
{
	if (docList != null)
	{
		var fileTotalSize = 0;
		for (var i=0; i < docList.size(); i++)
		{
			fileTotalSize += docList.get(i).getFileSize(); // unit: byte
		}
		return maxSize >= fileTotalSize;
	}
	return true;
}

function sendEmailForMeeting(calendarID, meetingID, emailSubject, emailContent,  docList)
{
	var resultAttendees = aa.meeting.getMeetingAttendees(calendarID, meetingID);
  var attendeeList = resultAttendees.getOutput();
  if (attendeeList && attendeeList.size() > 0)
  {
	   aa.print("Follow attendee will recieve email");
	   var objList = attendeeList.toArray();
	   var to = "";
     for (var i = 0; i < attendeeList.size(); i++)
     {
     	  var attendee = attendeeList.get(i);
     	  if (attendee.getEmail() != null && attendee.getEmail() != "")
     	  {
     	  	 to += attendee.getEmail() + ";";
     	  	 aa.print(attendee.getName() + ":" + attendee.getEmail());
     	  }
     }
     var from = "";
     // cc should separate by ';' or ','. exp: <EMAIL>;<EMAIL>
     // to should separate by ';' or ','. exp: <EMAIL>;<EMAIL>
     var cc = "";
     var result = aa.meeting.sendEmail(emailSubject, emailContent, from, to, cc, docList);
     return result;
  }
  return null;
}
