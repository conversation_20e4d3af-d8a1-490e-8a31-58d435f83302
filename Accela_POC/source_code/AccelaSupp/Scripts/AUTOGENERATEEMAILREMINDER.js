/*------------------------------------------------------------------------------------------------------/
| BEGIN Event Specific Variables
/------------------------------------------------------------------------------------------------------*/
var eventIDValue = aa.env.getValue("Exam_Session_ID");//eventID
var emailTemplateName = aa.env.getValue("Notification_Template");//templateType

var mailFrom = "<EMAIL>";
//Mail CC
var mailCC = "<EMAIL>";     
var examName;
var providerName;
var acaWebServiceSite = "http://aa-demo.achievo.com/710/DEMO";

/*------------------------------------------------------------------------------------------------------/
| END Event Specific Variables
/------------------------------------------------------------------------------------------------------*/

function main()
{
	if(eventIDValue == "" || emailTemplateName == "")
	{
		return false;
	}

	var eventID = aa.util.parseLong(eventIDValue);
	var rosterModelResult = aa.examination.getRosterModelListByEventID(eventID);

	// get Exam Name and Provider Name
	setExamNameAndProviderName(eventID);

	var successEmailNum = 0;
	var failEmailNum = 0;
	var EmailAddressEmtpyNum = 0;
	if(rosterModelResult.getSuccess())
	{
	  var rosterModelList = rosterModelResult.getOutput();
	  var it = rosterModelList.iterator();
	  while(it.hasNext())
	  {
			var rosterModel = it.next();
			
			var rosterID = rosterModel.getProviderRosterPKModel().getRosterNbr();
			var b1PerId1 = rosterModel.getB1PerId1();
			var b1PerId2 = rosterModel.getB1PerId2();
			var b1PerId3 = rosterModel.getB1PerId3();
			var altID = rosterModel.getAltID();
			var capIDModel = aa.cap.createCapIDScriptModel(b1PerId1, b1PerId2, b1PerId3);
			
			var eventModel = rosterModel.getProviderEventModel();
			var examDate = aa.util.formatDate(eventModel.getStartTime(),'dd\\MM\\yyyy');
			var examStartTime = aa.util.formatDate(eventModel.getStartTime(),'hh:mm aa');
			var examEndTime = aa.util.formatDate(eventModel.getEndTime(),'hh:mm aa');
			var address = eventModel.getrProviderLocationModel().getAddress1();
			//aa.print("Exam address:" + address +"\n");
			
			var emailParameters = aa.util.newHashtable();
		  
			addParameter(emailParameters, "$$PrimaryContactName$$", getCapPrimaryContactName(capIDModel));
			addParameter(emailParameters, "$$RecordType$$", getCapType(capIDModel));
			addParameter(emailParameters, "$$AlternateID$$", getAltID(capIDModel));
			addParameter(emailParameters, "$$ExaminationName$$", examName);
			addParameter(emailParameters, "$$ExaminationDate$$", examDate);
			addParameter(emailParameters, "$$ExaminationStartTime$$", examStartTime);
			addParameter(emailParameters, "$$ExaminationEndTime$$", examEndTime);
			addParameter(emailParameters, "$$ExaminationSite$$", address);
			addParameter(emailParameters, "$$Url$$", getACAUrl(capIDModel));
			var examModelResult = aa.examination.getExaminationModelByRosterID(rosterID);
			if(examModelResult.getSuccess() && examModelResult.getOutput() != null)
			{
				var examModel = examModelResult.getOutput();
				addParameter(emailParameters, "$$ExaminationComment$$", examModel.getComments());
				var examSiteModel = aa.examination.getExamSiteByExamModel(examModel);
				if(examSiteModel.getSuccess() && examSiteModel.getOutput() != null)
				{
					addParameter(emailParameters, "$$DrivingDirections$$", examSiteModel.getOutput().getDrivingDirections());
					addParameter(emailParameters, "$$AccessibilityDescription$$", examSiteModel.getOutput().getHandicapAccessible());
				}
				else
				{
					addParameter(emailParameters, "$$DrivingDirections$$", "");
					addParameter(emailParameters, "$$AccessibilityDescription$$", "");
				}
			}
			else
			{
				addParameter(emailParameters, "$$ExaminationComment$$", "");
				addParameter(emailParameters, "$$DrivingDirections$$", "");
				addParameter(emailParameters, "$$AccessibilityDescription$$", "");
			}
			
			var email = getEmailbyRosterID(capIDModel);
			
			if(email != "")
			{
			   var isSuccess = sendEmail(mailFrom, email, mailCC, emailTemplateName, emailParameters, capIDModel); 
			   if(isSuccess == true)
			   {
				  successEmailNum++;
			   }
			   else
			   {
				  failEmailNum++;
			   }
			}
			else
			{
			   aa.log("Send email address empty!");
			   EmailAddressEmtpyNum++;
			}  
	  }
	   aa.log("Send E-mail successful" + successEmailNum +" "
			  +"Send E-mail fail" + failEmailNum +", "
			  +"Send E-mail address empty " + EmailAddressEmtpyNum);
	}
}

//
function getEmailbyRosterID(capIDModel)
{
	var capContactResult = aa.cap.getCapPrimaryContact(capIDModel);
	if (capContactResult.getSuccess && capContactResult.getOutput() != null)
	{
		var capContact = capContactResult.getOutput();
		if (capContact != null && capContact.getPeople() != null && capContact.getPeople().getEmail())
		{
			//aa.print(capContact.getPeople().getEmail());
                        return capContact.getPeople().getEmail();
		}
	}
        return "";
}

function getACAUrl(capID)
{
	var acaUrl = "";
	if(capID == null || capID.getCapID() == null)
	{
		return acaUrl;
	}
	var capIDModel = capID.getCapID();
	var capResult = aa.cap.getCap(capIDModel.getID1(), capIDModel.getID2(), capIDModel.getID3());
	if(!capResult.getSuccess())
	{
		return acaUrl;
	}
	var cap = capResult.getOutput().getCapModel();
	acaUrl = acaWebServiceSite + "/urlrouting.ashx?type=1000";	
	acaUrl += "&Module=" + cap.getModuleName();
	acaUrl += "&capID1=" + capIDModel.getID1() + "&capID2=" + capIDModel.getID2() + "&capID3=" + capIDModel.getID3();
	acaUrl += "&agencyCode=" + aa.getServiceProviderCode();
	return acaUrl;
}

function getCapType(capIDModel)
{
	var capType = "";
	var capScriptModel = aa.cap.getCap(capIDModel.getCapID());
	if(capScriptModel.getSuccess())
	{
		capType = capScriptModel.getOutput().getCapType().toString();
	}
	return capType;
}

function getAltID(capIDModel)
{
	var altID = "";
	var capScriptModel = aa.cap.getCap(capIDModel.getCapID());
	if(capScriptModel.getSuccess())
	{
		altID = capScriptModel.getOutput().getCapModel().getAltID();
	}
	return altID;
}

function getCapPrimaryContactName(capIDModel)
{
	var capContactResult = aa.cap.getCapPrimaryContact(capIDModel);
	var contactName = "";
	if (capContactResult.getSuccess())
	{
		var capContact = capContactResult.getOutput();
		if (capContact != null)
		{
			if(capContact.getFirstName() != null)
			{
				contactName += capContact.getFirstName() + " ";		
			}
			if(capContact.getMiddleName() != null)
			{
				contactName += capContact.getMiddleName() + " ";
			}
			if(capContact.getLastName() != null)
			{
				contactName += capContact.getLastName();
			}
			return contactName;			
		}
	}
  return "";
}

  
//Add value to map.
function addParameter(pamaremeters, key, value)
{
	if(key != null)
	{
		if(value == null)
		{
			value = "";
		}	
		pamaremeters.put(key, value);
	}
}
  
function sendEmail(from, to, cc, emailTemplateName, emailParameters, capIDModel)
{
	var fileName = [];
	result = aa.document.sendEmailAndSaveAsDocument(from, to, cc, emailTemplateName, emailParameters, capIDModel, fileName);
	if(result.getSuccess())
	{
		aa.log("Send email "+to+" successfully!");
		return true;
	}
	else
	{
		aa.log("Fail to send mail.");
		return false;
	}
}

function setExamNameAndProviderName(eventID)
{
	var examMapResult = aa.examination.getExamNameAndProviderName(eventID);
	if(examMapResult.getSuccess() && examMapResult.getOutput() != null)
	{
	   var examMap = examMapResult.getOutput();
	   examName = examMap.get("examName");
	   providerName = examMap.get("providerName");
	}
}

main();
