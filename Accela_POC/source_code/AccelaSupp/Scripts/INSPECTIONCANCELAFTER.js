aa.print("InspectionCancelAfter emse script start.");

//Get inspection model list
var inspectionList= aa.env.getValue("InspectionList");

//Get product
var product= aa.env.getValue("Product");

// output the EMSE parameters for test
aa.print("product: " + product);

if (product == "ACA")
{
                if(inspectionList != null)
                {              
                                aa.print("inspectionList != null");
                                var its = inspectionList.iterator();

                                while(its.hasNext())
                                {
                                                aa.print("inspectionModel != null");

                                                var inspectionModel = its.next();
                                                var requiredInspection = inspectionModel.getActivity().getRequiredInspection();

                                                aa.print("requiredInspection=" + requiredInspection);
                                                if(requiredInspection == "Y")
                                                {
                                                                aa.print("requiredInspection == Y");

                                                                inspectionModel.getActivity().setIdNumber(0); // clear the inspection ID, otherwise the inspection will not be created but updated
                                                                inspectionModel.getActivity().setResStatus("Y"); // update the status to 'Y' because the status of canceled pending inspection is 'N'

                                                                // Note: below statement is only used by ACA, for other products, please remove or comment it. 
                                                                // Update the flag to indicate current inspection is created by V360 (public user could not cancel requested pending inspection).
                                                                // if you want to indicate current inspection is created by ACA (public user could cancel requested pending inspection), please remove current statement.
                                                                inspectionModel.getActivity().setCreatedByACA(null); 

                                                                aa.inspection.pendingInspection(inspectionModel);
                                                                
                                                                aa.print("create one pending inspection.");
                                                }
                                }
                }
}

aa.env.setValue("ScriptReturnCode", "0");
aa.env.setValue("ScriptReturnMessage", "Successfully.");

aa.print("InspectionCancelAfter emse script end.");
