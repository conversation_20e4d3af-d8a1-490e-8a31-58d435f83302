//Description:
//This sample script is used to move all agenda records from a meeting to another meeting.  (Reschedule meeting) 
//Event Name: 
//-----------------------------------------------------------------------------------------------------------

var originalMeetingID = 6266;
var meetingGroupID = 12 ;

//Get next available meeting
var meetings = getAvailableMeetings(meetingGroupID, originalMeetingID);

if(meetings && meetings.length > 0)
{
	//CAP ID list
	var originalAgendas = getAgendaItemsByMeetingID(meetingGroupID, originalMeetingID);

	for(var i = 0; i < meetings.length; i++)
	{ 
		var targetMeetingAgendas = getAgendaItemsByMeetingID(meetings[i].getMeetingGroupId(), meetings[i].getMeetingId());
		if(validation4MaxAgenda(originalAgendas.size(), targetMeetingAgendas.size(), meetings[i]))
		{
			//filter CAP ID.
			filterRepetitiveCapID(originalAgendas,targetMeetingAgendas);
			if(originalAgendas && originalAgendas.size() > 0)
			{
				//Move to next available meetings. 
				var result = aa.meeting.moveAgendaToAnotherMeeting(meetingGroupID, meetings[i].getMeetingId(), originalMeetingID, originalAgendas);
				if(result.getSuccess())
				{
					aa.print("Target meeting is "+meetings[i].getMeetingName()+". Meeting ID is " + meetings[i].getMeetingId());
					aa.env.setValue("ScriptReturnMessage", "Meetings are rescheduled successfully.");
					break;
				}
				else
				{
					aa.print("Move to " + meetings[i].getMeetingName() + ' fail!. Meeting ID is ' + meetings[i].getMeetingId());
				}
				
			}
			
		}
		if(i == (meetings.length-1))
		{
			aa.env.setValue("ScriptReturnMessage", "Meetings are moved fail! Can not find any available meeting");
		}
	}
}
else
{
	aa.env.setValue("ScriptReturnMessage", "Meetings are moved fail! Can not find any available meeting");
}

function validation4MaxAgenda(originalSize, targetSize, meetingModel)
{
	if(targetSize >= 0)
	{
		var targetFreeSize = meetingModel.getMaxUnits() - targetSize;
		aa.print('targetFreeSize:'+targetFreeSize);
		if(targetFreeSize >= originalSize)
		{
			return true;
		}
	}
	return false;
}

// Filter repetitive CapID.
function filterRepetitiveCapID(originalList, newList)
{
	if(newList && newList.size() > 0)
	{
		for(var i = 0; i < originalList.size(); i++)
		{
			if(newList.contains(originalList.get(i)))
			{
				aa.print("Agenda record " +originalList.get(i) + " exists in the target meeting.");
				originalList.remove(i);
			}
		}
	}
}

function getAvailableMeetings(meetingGroupID, meetingID)
{
	//Get meeting model and meeting group model.
	var meetingGroupResult  = aa.meeting.getMeetingCalendar(meetingGroupID);
	var meetingResult = aa.meeting.getMeetingByMeetingID(meetingGroupID, meetingID);
	if(!(meetingGroupResult.getSuccess() && meetingResult.getSuccess()))
	{
		return null;
	}
	var meetingGroup = meetingGroupResult.getOutput();
	var meetingModel = meetingResult.getOutput();
	//Get new available meeting model.
	var nextMeetingResult = aa.meeting.getAvailableMeetings(meetingModel.getMeetingBody(), 0.0, 
						meetingGroup.getMeetingGroupName(), convertDate(new java.util.Date()),null,null,null);
	
	if(nextMeetingResult.getSuccess())
	{
		
		var meetingModels = nextMeetingResult.getOutput();
		if(meetingModels.length > 0)
			return meetingModels;
	}
	return null;
}

function getAgendaItemsByMeetingID(meetingGroupID, meetingID)
{
	var agendaResult = aa.meeting.getMeetingAgendaItems(meetingGroupID, meetingID);
	if(agendaResult.getSuccess())
	{
		return agendaResult.getOutput();
	}
	else
	{
		aa.print('Failed to get meeting agenda records. MeetingID is ' + meetingID);
		return null;
	}
}

function convertDate(date)
{
	var cal=java.util.Calendar.getInstance();
	cal.setTime(date);
	return new com.accela.aa.emse.util.ScriptDateTime(cal);
}


function getCapTypeModel(capIDModel)
{
	var capTypeResult = aa.cap.getCapTypeModelByCapID(capIDModel);
	if(capTypeResult.getSuccess())
	{
		return capTypeResult.getOutput();
	}
	return null;
}
