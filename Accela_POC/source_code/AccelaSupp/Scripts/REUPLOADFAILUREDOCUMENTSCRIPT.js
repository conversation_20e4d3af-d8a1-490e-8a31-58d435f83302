/**
 * <pre>
 * 
 *  Accela Automation
 *  File: ReuploadFailureDocumentScript.js
 * 
 *  Accela, Inc.
 *  Copyright (C): 2014
 * 
 *  Description:
 *  TODO
 * 
 *  Notes:
 * 	Id: ReuploadFailureDocumentScript.js 72642 2009-01-01 20:01:57Z ACHIEVO\andy.chen $ 
 * 
 *  Revision History
 *  <Date>,			<Who>,			<What>
 *  Cot 31 2014 		Andy <PERSON>		Initial.
 * </pre>
 */
 //reupload the failure documents, and you can use batch job to run this script.
aa.failureDocument.reuploadFailureDocuments();
