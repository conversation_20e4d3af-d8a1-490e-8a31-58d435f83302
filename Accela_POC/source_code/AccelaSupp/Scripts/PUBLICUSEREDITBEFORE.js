/**
 * <pre>
 * 
 *  Accela Automation
 *  File: PublicUserEditBefore.js
 * 
 *  Accela, Inc.
 *  Copyright (C): 2014
 * 
 *  Description:
 *  TODO
 * 
 *  Notes:
 * 	Id: PublicUserEditBefore.js $
 * 
 *  Revision History
 *  <Date>,			<Who>,			<What>
 *  Aug 11 2014 		<PERSON>.
 * </pre>
 */
var servProvCodes = new Array('JEFF','JEREMYCH<PERSON>');//initialize agency codes. not include super agency
var publicUser = aa.env.getValue("PublicUserModel");
var callerId = aa.env.getValue("callerId");
