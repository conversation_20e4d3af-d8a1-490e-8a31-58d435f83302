/**
The Sync flag indicates if a record contact is and needs to be in sync with the corresponding reference contact. 
The setSyncFlag ("N") function enables you to stop sync at any time. It is not meant to do the sync. 
aa.people.syncCapContactFromReference (capContact,people) or 
aa.people.syncCapContactToReference (capContact,people) is the proper function to call, when you need to do the sync.
*/

var capID = null;
var capIDResult = aa.cap.getCapID("13CAP","00000","0024S");
if(capIDResult.getSuccess())
{
	capID = capIDResult.getOutput();
}
else
{
	aa.print(capIDResult.getErrorMessage());
}

if(capID != null)
{
	var capContactResult = aa.people.getCapContactByCapID(capID); 

	if (capContactResult.getSuccess()) 
	{

		var capContactArray = capContactResult.getOutput();
	
		for (var i in capContactArray) 
		{
			thisContact = capContactArray[i];

			thisContact.getCapContactModel().setSyncFlag("N");
			var result = aa.people.updateCapContactSyncFlag(thisContact.getCapContactModel());
			var contactSeqNum = result.getOutput();
			if (result.getSuccess())
		    		aa.print("The contact was updated successfully! The Contact Number is " + contactSeqNum + ".");
			else
				aa.print("The contact was updated failed! The Contact Number is " + contactSeqNum + ". " + result.getErrorMessage());

		}

	}
}
