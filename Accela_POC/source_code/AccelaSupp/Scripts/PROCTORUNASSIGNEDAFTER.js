/*---- Inital environment parameters ----*/
var examSite = aa.env.getValue("examinationSite");
var peopleID = aa.env.getValue("PeopleID");
var scheduleDate = aa.env.getValue("PageScheduleDate");
var examName = aa.env.getValue("examinationName");
var providerName =aa.env.getValue("providerName");
var startTime = aa.env.getValue("startTime");
var endTime = aa.env.getValue("endTime");
var proctorName = "";
var serviceProviderCode = "";
/*---- Inital environment parameters end----*/

/*---- User intial parameters ----*/
var templateName ="NOTICE OF UNASSIGNMENT";
var from ="<EMAIL>";
var cc = "";
/*---- User intial parameters end----*/

function sendMail()
{
	var peopleReturn = aa.people.getPeople(peopleID);
	var to = "";
	var fileNames = [];
	if(peopleReturn != null)
	{
		var people = peopleReturn.getOutput();
		to = people.getEmail();
		proctorName = getProctorName(people);
		serviceProviderCode = people.getServiceProviderCode();
		if(to != "")
		{
			var isSuccess = aa.document.sendEmailByTemplateName(from, to, cc, templateName, getParams(), fileNames);
			if(isSuccess.getSuccess())
			{
				aa.print("Sent email successfully.");
			}
			else
			{
				aa.print("Sent email failed.");
			}
		}
		else
		{
			aa.print("Email address is empty.");
		}
		
	}
}

function getParams()
{
	var params = aa.util.newHashtable();
	addParameter(params, "$$startTime$$",startTime );
	addParameter(params, "$$endTime$$",endTime );
	addParameter(params, "$$proctorName$$",proctorName );
	addParameter(params, "$$examSite$$", examSite);
	addParameter(params, "$$examName$$",examName );
	addParameter(params, "$$providerName$$",providerName );
	addParameter(params, "$$serviceProviderCode$$",serviceProviderCode );
	return params;
}

function getProctorName(peopleModel)
{
	var name = "";
	if(peopleModel.getFullName() != null)
	{
		name = peopleModel.getFullName();
	}
	else
	{	
		var first = "";
		var middle = "";
		var last = "";
		if(peopleModel.getFirstName()!=null) 
		{
			first = peopleModel.getFirstName() + " ";
		}
		if(peopleModel.getMiddleName() != null)
		{
			middle = peopleModel.getMiddleName() + " ";
		}
		if(peopleModel.getLastName())
		{
			last = peopleModel.getLastName();
		}
		
		name = first + middle + last;
		
	}
	return name;
}

function addParameter(pamaremeters, key, value)
{
	if(key != null)
	{
		if(value == null)
		{
			value = "";
		}
		pamaremeters.put(key, value);
	}
}


function main()
{
	 sendMail();	
}

aa.env.setValue("ScriptReturnCode","0");
aa.env.setValue("ScriptReturnMessage", "successful");
main();
