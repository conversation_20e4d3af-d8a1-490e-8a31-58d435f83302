aa.print("------------------invoke 'aa.cap.isPendingValidationRecord' method---------------------------------------------------");

capID1="14CAP";
capID2="00000";
capID3="002CF";
//Construct CapIDModel object
capIDObj = aa.cap.getCapID(capID1,capID2,capID3).getOutput();
//According CapIDModel to check the record is wether enforced pending validation.
rs= aa.cap.isPendingValidationRecord(capIDObj);
//If "isPendingValidationRecord" method is successfully invoked that "getSuccess" method will return true.Otherwise, return false.
if(rs.getSuccess())
{
  aa.print("success");
  //"getOutput" method will return boolean value. 
  //If value is true, the record was enforced pending validation.
  //If value is false, the record isn't enforced pending validation.
  if(rs.getOutput().booleanValue())
  {
    aa.print("Result:true");
  }
  else
  {
    aa.print("Result:false");
  }
 aa.print(rs.getErrorMessage());
}
else
{
  aa.print("fail");
  aa.print("ERROR: " + rs.getErrorMessage()); 
}
aa.print("------------------invoke 'capScriptModel.getPendingValidation' method---------------------------------------------------");
//Search cap object
rs2 = aa.cap.getCap(capID1,capID2,capID3);
if(rs2.getSuccess())
{
  aa.print("success");
  capScriptModel = rs2.getOutput();
  if(capScriptModel != null)
  {
    if (capScriptModel.getPendingValidation() == null) 
    {
      aa.print("Result: pending validation column's value is null.");
    }
    else if (capScriptModel.getPendingValidation() == 'Y')
    {
      aa.print("Result: pending validation column's value is Y.");
    }
  }
}
else
{
  aa.print("fail");
  aa.print("ERROR: " + rs.getErrorMessage()); 
}
aa.print("------------------invoke 'capScriptModel.setPendingValidation' method---------------------------------------------------");
if(rs2.getSuccess())
{
  aa.print("success");
  capScriptModel = rs2.getOutput();
  if(capScriptModel != null)
  {
    aa.print("Current pending validation column's value is " + capScriptModel.getPendingValidation() + " in capScriptModel.");
    if (capScriptModel.getPendingValidation() == null) 
    {
      capScriptModel.setPendingValidation("Y");
      aa.print("Set 'Y' value to pending validation column of capScriptModel.");
    }
    else if (capScriptModel.getPendingValidation() == 'Y')
    {
      capScriptModel.setPendingValidation(null);
      aa.print("Set null value to pending validation column of capScriptModel.");
    }
   aa.print("Result: pending validation column's value is " + capScriptModel.getPendingValidation()) + " in capScriptModel.";
  } 
}
else
{
  aa.print("fail");
  aa.print("ERROR: " + rs.getErrorMessage()); 
}
