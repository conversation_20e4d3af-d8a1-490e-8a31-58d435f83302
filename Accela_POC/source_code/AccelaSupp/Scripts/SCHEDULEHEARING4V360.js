/*------------------------------------------------------------------------------------------------------/
|
|     This is an EMSE example of how to schedule hearing for V360.
|			It related 'WorkflowTaskUpdateAfter' event as an example.
|			
|			The variables in the following must be provided.
|			var isSuccess = aa.calendar.scheduleHearing4V360(capID,calendarID,eventID,duration,reason,comments);
|			1,capID(Required)----The record ID
|			2,calendarID(Required)----The calendar ID which you want to schedule.you can get it from Avaliable Hearing.
|			3,eventID(Required)----The eventID which you want to schedule.you can get it from Avaliable Hearing.
|			4,duration(Required)----The duration that you want to schedule. 
|			5,reason(Option)----The hearing reason
|    	6,comments(Option)----The hearing comments
|     
/------------------------------------------------------------------------------------------------------*/

var wfTask = aa.env.getValue("WorkflowTask");		// Workflow Task Triggered event

if(wfTask.equals("Incomplete"))
{
	doScheduleHearing();
}

function doScheduleHearing()
{
	var calendarID;
	var eventID;
	var capID = getCapId();
	var hearingBody = null;
	var duration = 0;
	var calendarName = null;
	var dateFrom = aa.date.parseDate('04/01/2012');
	var dateTo = aa.date.parseDate('05/01/2013');
	var dayOfWeek = null;// 0~6
	var location = null;
	var comments = null;
	var reason = null;
	var result = aa.calendar.getAvailableHearingItem(hearingBody, duration,
	calendarName, dateFrom, dateTo, dayOfWeek, location)
	if(result.getSuccess())
	{
	  var hearingItems = result.getOutput();
	  aa.print("Items length:" + hearingItems.length);
	  for(var i =0 ; i < hearingItems.length; i++)
	  {
	    var hearingItem = hearingItems[i];
	    calendarID = hearingItem.calendarID;
	    eventID = hearingItem.eventID;
	    aa.print(calendarID);
	    aa.print(eventID);
	    //
	    break;
	  }
	  var isSuccess = aa.calendar.scheduleHearing4V360(capID,calendarID,eventID,duration,reason,comments);
		if(isSuccess.getSuccess())
		{
			aa.env.setValue("ScriptReturnCode","0");
			aa.env.setValue("ScriptReturnMessage", "Schdule hearing successful");
		}
	}
	else
	{
	  aa.print("get available hearing failed");
	}
	
}

function getCapId()
{
  var s_id1 = aa.env.getValue("PermitId1");
  var s_id2 = aa.env.getValue("PermitId2");
  var s_id3 = aa.env.getValue("PermitId3");

  var s_capResult = aa.cap.getCapID(s_id1, s_id2, s_id3);
  if(s_capResult.getSuccess())
    return s_capResult.getOutput();
  else
  {
    logMessage("**ERROR: Failed to get capId: " + s_capResult.getErrorMessage());
    return null;
  }
}

