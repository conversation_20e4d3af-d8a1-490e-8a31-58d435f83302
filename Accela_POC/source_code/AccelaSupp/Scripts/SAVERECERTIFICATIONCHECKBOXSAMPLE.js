/*
 *Description:To save certification info.
 *  Less one ASI group is need to associated to record type, and two fields label must fixed the configuration.
 *  If field label changed, please replace them.
 *  check box field:'Certification Accetpted'
 *  date field: 'Certification Date'
 */

doCertification();
function doCertification() {
	// ASI fields label need to be saved
	// The check box label field
	var certificationCheckLabel = "Certification Accepted";
	// The date label field
	var certificationDateLabel = "Certification Date";
	
	var capId = null;
	var sca_id1 = aa.env.getValue("PermitId1");
	var sca_id2 = aa.env.getValue("PermitId2");
	var sca_id3 = aa.env.getValue("PermitId3");
	
	if (sca_id1 != null && sca_id1 != "" && sca_id2 != null && sca_id2 != ""
			&& sca_id3 != null && sca_id3 != "")
		capId = aa.cap.getCapID(sca_id1, sca_id2, sca_id3).getOutput();

	var publicUserID = aa.env.getValue("CurrentUserID");
	
	var needReCertification = aa.acaPageFlow.isReCertificationRequired(capId, publicUserID);
	var isReCertificationRequired = false;
	if (needReCertification.getSuccess())
		isReCertificationRequired = needReCertification.getOutput();

	if ("false" != isReCertificationRequired.toString()) {
		var appSpecInfoResult = aa.appSpecificInfo.getByCapID(capId);
		if (appSpecInfoResult.getSuccess()) {
			var fAppSpecInfoObj = appSpecInfoResult.getOutput();

			for (loopi in fAppSpecInfoObj) {
				var appSpecificField = fAppSpecInfoObj[loopi];
				var editAppSpecificInfo = false;
				if (appSpecificField != null
						&& appSpecificField.getFieldLabel() == certificationCheckLabel) {
					appSpecificField.setChecklistComment("CHECKED");
					editAppSpecificInfo = true;
				}
				else if(appSpecificField != null && appSpecificField.getFieldLabel() == certificationDateLabel) {
					var dt = aa.date.getCurrentDate();					
					appSpecificField.setChecklistComment(dt.getMonth()+"/"+dt.getDayOfMonth()+"/"+dt.getYear());
					editAppSpecificInfo = true;
				}

				if (editAppSpecificInfo)
					aa.appSpecificInfo.editAppSpecInfoValue(appSpecificField);

			}
		}
	}
}
