
var contactModel = aa.env.getValue("Contact");
var newSelectedContactType = aa.env.getValue("selectedContactType");
var oldContactType = contactModel.getPeople().getContactType();

if("Applicant".equals(newSelectedContactType))
{
	aa.env.setValue("ScriptReturnCode", "0");
	aa.env.setValue("ScriptReturnMessage", "Change contact type to Applicant.");
 
}
else
{
	aa.env.setValue("ScriptReturnCode", "-1");//return "-1" means change failed.
	aa.env.setValue("ScriptReturnMessage", "Change contact type failed, because you do not meet the conditions.");
}
