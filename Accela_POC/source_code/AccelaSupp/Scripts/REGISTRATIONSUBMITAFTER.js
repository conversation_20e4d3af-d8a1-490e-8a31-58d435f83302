//Description:
//This sample script is used to update a public user account and associate disciplines for this user after connect a license. 
//Event Name: RegistrationSubmitAfter
//-----------------------------------------------------------------------------------------------------------

aa.print("-------------------START--------");
var publicUser = aa.env.getValue("PublicUserModel");
if (publicUser != null)
{
		var serviceProviderCode = publicUser.getServProvCode();
		var userDisciplineModel = aa.publicUser.getUserDisciplineModel().getOutput();
		var userDistrictModel = aa.publicUser.getUserDistrictModel().getOutput();
		var licenseTypes = aa.env.getValue("licenseTypes");
		
		var userModel = aa.publicUser.getUserModel().getOutput();
		var userName = "PUBLICUSER" + publicUser.getUserSeqNum();
		
		userModel.setServProvCode(serviceProviderCode);
		userModel.setUserName(userName);
		//Get current public user AA account.
		var dbUserModel = aa.publicUser.getPublicUserAAAccount(userModel).getOutput();
		
		dbUserModel.setStatus("ENABLE");
		dbUserModel.setInspector("Y");
		dbUserModel.setDailyInspUnits(5);
		//update current public user AA account.
		aa.publicUser.updatePublicUserAAAccount(dbUserModel);		

		//set current public user to self certified inspector
		publicUser.setAccountType("SELF_CERTIFIED_INSPECTOR");
		aa.publicUser.editPublicUser(publicUser);
		
		for(var i=0; i<licenseTypes.length; i++)
		{
			// If LP type is 'Boilermaker', set the discipline as 'Boiler' and District is 'East'.
			var  lpType = licenseTypes[i];
			if ( lpType == 'Boilermaker')
			{
				 // Add Discipline to public user
				 userDisciplineModel.setDiscipline("Boiler");
		     userDisciplineModel.setUserId(userName);
		     userDisciplineModel.setRecFulNam("PUBLICUSER");
		     aa.publicUser.addPublicUserAAAccountDiscipline(userDisciplineModel);
		     
		      // Add District to public user
		     userDistrictModel.setUserName(userName);
		     userDistrictModel.setRecFulName("PUBLICUSER");
		     userDistrictModel.setDistrict("NYC East");
		     aa.publicUser.createPublicUserDistrict(userDistrictModel);
		     
			}
			else if ( lpType == 'Other Type')
			{
				 // Add Discipline to public user
				 userDisciplineModel.setDiscipline("Other");
		     userDisciplineModel.setUserId(userName);
		     userDisciplineModel.setRecFulNam("PUBLICUSER");
		     aa.publicUser.addPublicUserAAAccountDiscipline(userDisciplineModel);
		     
		      // Add District to public user
		     userDistrictModel.setUserName(userName);
		     userDistrictModel.setRecFulName("PUBLICUSER");
		     userDistrictModel.setDistrict("NYC West");
		     aa.publicUser.createPublicUserDistrict(userDistrictModel);
			}			
		}
				
		aa.print("-------------------END--------");
}
