//Creat a SetHeader.

var setscript = aa.set.getSetHeaderScriptModel().getOutput();

var SetID = "EMSE SETDI1026";
var SetTitle = "EMSE SET1026";
var SetStatusComment = "Create a Set by EMSE";
var SetStatus = "Pending";
var SetType = "All Given";

setscript.setSetID(SetID);
setscript.setSetTitle(SetTitle);
setscript.setSetStatusComment(SetStatusComment);
setscript.setSetStatus(SetStatus);
setscript.setRecordSetType(SetType);
setscript.setServiceProviderCode("NYC");
setscript.setAuditDate(aa.date.getCurrentDate());
setscript.setAuditID("Admin");

setscript = aa.set.createSetHeader(setscript);

// Update the SetHeader.
setscript.getOutput().setSetTitle("EMSE Update");
setscript.getOutput().setSetStatusComment("Update");
setscript.getOutput().setSetStatus("Update");
setscript.getOutput().setRecordSetType("Other");


var updateSet = aa.set.updateSetHeader(setscript.getOutput());
if (updateSet.getSuccess())
{
	aa.print("updateSetHeader Successfully");
}
else
{
	aa.print("updateSetHeader Failed");
  aa.print(updateSet.getErrorMessage());	
}

