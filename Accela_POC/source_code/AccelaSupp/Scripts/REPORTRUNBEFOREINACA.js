var reportInfo = aa.env.getValue("ReportInfoModel");
var reportDetail = aa.env.getValue("ReportDetailModel");

//defind reprint log ASIT name.
var rePrintLogTableName = "REPRINT LOG";

var maxReprintTimes = 0;

if(reportDetail)
{
  // get max reprint time from report configuration.
	maxReprintTimes = reportDetail.getReprintLimit4ACA();	
}

var capIDModel;

if(reportInfo)
{
	var reportParameters = reportInfo.getReportParameters();
	var edmsEntityModel = reportInfo.getEDMSEntityIdModel();
	if(edmsEntityModel)
	{
		// build cap id model
		capIDModel = getCapID(edmsEntityModel.getCapId());
		if(capIDModel)
		{
			var result = aa.appSpecificTableScript.getAppSpecificTableModel(capIDModel,rePrintLogTableName);
			if(result.getSuccess())
			{
				var asit = result.getOutput().getAppSpecificTableModel();
				if(asit.getTableField() != null && asit.getTableField().size()>0)
				{		
				  // because we insert one row into reprint log table every print, 
				  // so the print times should be the record numbers in print log table.
					var rePrintTimes = asit.getTableField().size()/asit.getColumns().size();	
					
					// if already used all the reprint times, return error message and code.
					if(parseInt(rePrintTimes) + 1 > maxReprintTimes)
					{      						
						aa.env.setValue("ScriptReturnMessage", "You can't reprint!");
						aa.env.setValue("ScriptReturnCode", "-1");
					}				
				}
			}
		}	
	}	 
}

function getCapID(capIDString)
{
	var capID;
	if(capIDString)
	{
		var capIDArray = capIDString.split("-");
		if(capIDArray.length == 3)
		{
			var capIDResult = aa.cap.getCapID(capIDArray[0],capIDArray[1],capIDArray[2]);
			if(capIDResult.getSuccess())
			{
				capID = capIDResult.getOutput();
			}
		}
	}
	return capID;
}
