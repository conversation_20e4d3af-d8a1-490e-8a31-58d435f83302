aa.print("=================Create New Start=================");
aa.print("=================AccountID|Amount|TransactionType|TransactionID=================");
var returnList = aa.env.getValue("transactionList");
var size = returnList.size();
for(var n = 0; n < size; n++)
{
	aa.print(returnList.get(n).getAcctID() + "|" + returnList.get(n).getTransAmount() + "|" + returnList.get(n).getTransType() + "|" + returnList.get(n).getTransSeq());
}
aa.print("=================Create New End=================");
