/**
 * <pre>
 * 
 *  Accela Automation
 *  File: RemoveParticalCapDocumentScript.js
 * 
 *  Accela, Inc.
 *  Copyright (C): 2014
 * 
 *  Description:
 *  You can use this script in batch job to remove the failure documents.
 * 
 *  Notes:
 * 	Id: RemoveParticalCapDocumentScript.js 72642 2009-01-01 20:01:57Z ACHIEVO\andy.chen $ 
 * 
 *  Revision History
 *  <Date>,			<Who>,			<What>
 *  Cot 31 2014 		<PERSON>.
 * </pre>
 */
//get three months before date
var startDate = aa.util.dateDiff(aa.util.now(), "day", -90);
//get one months before date
var endDate =   aa.util.dateDiff(aa.util.now(), "day", -30);
aa.failureDocument.removeFailurePartialCapDocumentByRange(startDate,endDate);
