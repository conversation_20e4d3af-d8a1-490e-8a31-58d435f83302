var debug = "";	
var br = "<BR>";
var message = "";

aa.print("***Begin to lookup up time accounting group name and type name");

var groupsAndTypes = getTimeGroupAndType('APCD', 'Air Pollution Control Aide','LUEG-APCD/Administrative/Change/Inactivation');

aa.print("groupsAndTypes size = "+groupsAndTypes.length);
for (xx in  groupsAndTypes)
{
	aa.print("groupsAndType info as below:");
	aa.print(" TimeGroupName:"+groupsAndTypes[xx].getTimeGroupName());
	aa.print(" TimeGroupSeq:"+groupsAndTypes[xx].getTimeGroupSeq());
	aa.print(" TimeTypeName:"+groupsAndTypes[xx].getTimeTypeName());
	aa.print(" TimeTypeSeq:"+groupsAndTypes[xx].getTimeTypeSeq());
	aa.print(" RecordType:"+groupsAndTypes[xx].getRecordType());
	aa.print(" BillableFlag:"+groupsAndTypes[xx].getBillableFlag());
	aa.print(" DefaultRate:"+groupsAndTypes[xx].getDefaultRate());
	aa.print(" DefaultPctAdj:"+groupsAndTypes[xx].getDefaultPctAdj());
	aa.print(" CapTypeModel:"+groupsAndTypes[xx].getCapTypeModel());
	aa.print("========================================");
}

aa.print("***End to lookup up time accounting group name and type name");


aa.print("***Begin to lookup up time accounting groupTypeModel");
var timeGroupTypeScriptModelResult = aa.timeAccounting.getTimeGroupTypeModel();
var timeGrpTypModel = timeGroupTypeScriptModelResult.getOutput();
timeGrpTypModel.setDefaultRate(80);
timeGrpTypModel.setBillableFlag('Y');
timeGrpTypModel.setTimeGroupName('APCD');
groupsAndTypes = getTimeGroupAndTypeInfo(timeGrpTypModel);

aa.print("groupsAndTypes size = "+groupsAndTypes.length);
for (xx in  groupsAndTypes)
{
	aa.print("groupsAndType info as below:");
	aa.print(" TimeGroupName:"+groupsAndTypes[xx].getTimeGroupName());
	aa.print(" TimeGroupSeq:"+groupsAndTypes[xx].getTimeGroupSeq());
	aa.print(" TimeTypeName:"+groupsAndTypes[xx].getTimeTypeName());
	aa.print(" TimeTypeSeq:"+groupsAndTypes[xx].getTimeTypeSeq());
	aa.print(" RecordType:"+groupsAndTypes[xx].getRecordType());
	aa.print(" BillableFlag:"+groupsAndTypes[xx].getBillableFlag());
	aa.print(" DefaultRate:"+groupsAndTypes[xx].getDefaultRate());
	aa.print(" DefaultPctAdj:"+groupsAndTypes[xx].getDefaultPctAdj());
	aa.print(" CapTypeModel:"+groupsAndTypes[xx].getCapTypeModel());
	aa.print("========================================");
}

aa.print("***End to lookup up time accounting groupTypeModel");

aa.print(debug);
aa.print(message);

function getTimeGroupAndType(groupName, typeName) // optional CapType String
{//Get the time accounting group and type info according to the specific group name and type name
	if (arguments.length < 3) {
		timeGroupTypeResult = aa.timeAccounting.getTimeGroupTypeModels(groupName, typeName);
		if (!timeGroupTypeResult.getSuccess())
		{
		logDebug("**ERROR: Failed to get time accounting group and type info: " + timeGroupTypeResult.getErrorMessage());
		return false;
		}
		return timeGroupTypeResult.getOutput();
	}
	else {
		var timeGroupTypeScriptModelResult = aa.timeAccounting.getTimeGroupTypeModel();
		var timeGrpTypModel = timeGroupTypeScriptModelResult.getOutput();
		timeGrpTypModel.setTimeGroupName(groupName);
		timeGrpTypModel.setTimeTypeName(typeName);
		var pCapType = arguments[2];
		var typeArray = pCapType.split("/");
		if (typeArray.length != 4) {
			logDebug("**ERROR in childGetByCapType function parameter.  The following cap type parameter is incorrectly formatted: " + pCapType);
		
		} else {
			_captype = aa.cap.getCapTypeModel().getOutput();
			_captype.setServiceProviderCode(timeGrpTypModel.getServiceProviderCode());
			_captype.setGroup(typeArray[0]);
			_captype.setType(typeArray[1]);
			_captype.setSubType(typeArray[2]);
			_captype.setCategory(typeArray[3]);
			timeGrpTypModel.setCapTypeModel(_captype);
		}

		timeGroupTypeResult = aa.timeAccounting.getTimeGroupTypeModels(timeGrpTypModel);
		if (!timeGroupTypeResult.getSuccess())
		{
		logDebug("**ERROR: Failed to get time accounting group and type info: " + timeGroupTypeResult.getErrorMessage());
		return false;
		}
		return timeGroupTypeResult.getOutput();
	}	
}

function getTimeGroupAndTypeInfo(_timeGrpTypModel)
{//Get the time accounting group and type info according to the specific TimeGroupTypeScriptModel instance
	timeGroupTypeResult = aa.timeAccounting.getTimeGroupTypeModels(_timeGrpTypModel);
		if (!timeGroupTypeResult.getSuccess())
		{
		logDebug("**ERROR: Failed to get time accounting group and type info: " + timeGroupTypeResult.getErrorMessage());
		return false;
		}
		return timeGroupTypeResult.getOutput();
}

function logDebug(dstr)
	{
	debug+=dstr + br;
	}
	
function logMessage(dstr)
	{
	message+=dstr + br;
	}
