/***********************************************************************
 * Accela Automation
 * File: 
 * Accela, Inc.
 * Copyright (C): 2011
 * 
 * Description: This is EMSE script  for update Ref. LP.
 *  
 *  Precondition:
 *  1. an existing People Info Table 
 *		Example in this sample script:
 *		- infoTableSubGroupCode : Info Table Sub Group Code
 *		- infoTableColumnName   : Column name in Info Table
 *		- rowIndex		: which row need to change value in Info Table
 *		- newRowValue		: What's the value after changed.
 *
 *		- licType		:licene Type
 *		- LicNbr		:Licene Sequence Number
 *		 
 *		
 * Notes:
 *
 **********************************************************************/
aa.print("============Ref LP debug start============");

var infoTableSubGroupCode = 'REBECCA';
var infoTableColumnName = 'rebecca';
var rowIndex = 0;
var newRowValue = '200';

var servProvCode = 'SACRAMENTO';
var licType = 'Architect';
var licNbr = 907761;

//1. Get InfoTableGroupCodeModel
var s_result = aa.licenseScript.getRefLicenseProfBySeqNbr(servProvCode,licNbr);
if (s_result)
{	
	var licenseObj =  s_result.getOutput();
	
	//1.1. Get People Info Table 
	var infoTableGroupCodeObj = getRefPeopleInfoTables(licenseObj);
	
	//1.2. Get InfoTableSubGroupCode
	var infoTableSubGroupCodes= getInfoTableSubGroups(infoTableGroupCodeObj);
	
	//1.3. Get InfoTableColumnModels
	var infoTableColumnModels = getInfoTableColumnsBySubGroup(infoTableSubGroupCodes,infoTableSubGroupCode);
	
	//1.4. Get all Info Table' values by its column name
	var infoTableValueModels =  getColumnValuesByName(infoTableColumnModels,infoTableColumnName);
	

	//1.5. Reset Into Table column values
	resetColumnValueByRowIndex(infoTableValueModels,rowIndex);
		
	//1.6. Execute update action
	updateRefInfoTable(licenseObj)

}
else
{
	aa.print("Not any People Info Table");
}


function getRefPeopleInfoTables(licenseObj)
{
	var infoTableGroupCodeObj = null;
	if (licenseObj)
	{
		infoTableGroupCodeObj = licenseObj.getInfoTableGroupCodeModel();
	}
	else
	{
		aa.print("Not any People Info Table");
	}
	
	return infoTableGroupCodeObj;
}

function getInfoTableSubGroups(infoTableGroupCodeObj)
{
	var infoTableSubGroups= null;
	if(infoTableGroupCodeObj != null )
	{
		infoTableSubGroups = infoTableGroupCodeObj.getSubgroups();
	}
	else
	{
		aa.print("Not any infoTableSubGroups");
	}

	return infoTableSubGroups;
}

function getInfoTableColumnsBySubGroup(infoTableSubGroupCodes,infoTableSubGroupCode)
{
	var infoTableColumnModels = null;
	if(infoTableSubGroupCodes !=  null)
	{
		var infoTableSubGroupCodeArray = infoTableSubGroupCodes.toArray();
		for (i in infoTableSubGroupCodeArray)
		{
			if ( infoTableSubGroupCode.equals(infoTableSubGroupCodeArray[i].getName()))
			{
				infoTableColumnModels = infoTableSubGroupCodeArray[i].getColumnDefines()
				break;
			}
		}
	}
	
	return infoTableColumnModels;
}

function getColumnValuesByName(infoTableColumnModels,infoTableColumnName)
{
	var infoTableValueModels = null;
	if (infoTableColumnModels != null)
	{
		var infoTableColumnArray = infoTableColumnModels.toArray();
		aa.print("infoTableColumnArray Length is:"+infoTableColumnArray.length);


		for (i in infoTableColumnArray)
		{
			if(infoTableColumnName.equals(infoTableColumnArray[i].getName()))
			{
				infoTableValueModels = infoTableColumnArray[i].getTableValues();
				break;
			}
		}
	}

	return infoTableValueModels;
}

function resetColumnValueByRowIndex(infoTableValueModels,rowIndex)
{
	if(infoTableValueModels!= null)
	{
		var infoTableValueArray = infoTableValueModels.toArray();
		//For output all values
		for (i in infoTableValueArray)
		{
			aa.print("rowNumber is:"+ infoTableValueArray[i].getRowNumber() +" columnNumber is"+infoTableValueArray[i].getColumnNumber() +" Value is:"+ infoTableValueArray[i].getValue());
		}
		
		//Reset value by row No.
		for(j in infoTableValueArray)
		{
			if(rowIndex == infoTableValueArray[j].getRowNumber())
			{
				infoTableValueArray[j].setValue(newRowValue);
			}
		}
	}
	
}

function updateRefInfoTable(licenseObj)
{
	if(licenseObj!=null)
	{
		//Update Info Table
		aa.licenseScript.editRefLicenseProf(licenseObj);
	}
	else
	{
		aa.print("Not any People Info Table to update");
	}
}

aa.print("============Ref LP debug end============");
