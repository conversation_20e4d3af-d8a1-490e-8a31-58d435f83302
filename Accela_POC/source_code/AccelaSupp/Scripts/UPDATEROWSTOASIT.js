var capID = "14CAP-00000-00B7A";
var capIDModel = aa.cap.getCapIDModel('14CAP', '00000', '00B7A').getOutput();

// table name
var tableName = "BUNNIASIT";
// Create a HashMap.
var searchConditionMap = aa.util.newHashMap(); // Map<columnName, List<columnValue>>
// Create a List object to add the value of Column.
var columnName ="column1";
var valuesList = aa.util.newArrayList();
valuesList.add("test");
valuesList.add("999");
searchConditionMap.put(columnName, valuesList);

var appSpecificTableInfo = aa.appSpecificTableScript.getAppSpecificTableInfo(capIDModel, tableName, searchConditionMap/** Map<columnName, List<columnValue>> **/);
if (appSpecificTableInfo.getSuccess())
{
	var appSpecificTableModel = appSpecificTableInfo.getOutput().getAppSpecificTableModel();
	var tableFields = appSpecificTableModel.getTableFields(); // List<BaseField>
	if (tableFields != null && tableFields.size() > 0)
	{
		var updateRowsMap = aa.util.newHashMap(); // Map<rowID, Map<columnName, columnValue>>
		for (var i=0; i < tableFields.size(); i++)
		{
			var fieldObject = tableFields.get(i); // BaseField
			//get the column name.
			var columnName = fieldObject.getFieldLabel();
			//get the value of column
			var columnValue = fieldObject.getInputValue();
			//get the row ID 
			var rowID = fieldObject.getRowIndex();
			aa.print(columnName + ": " + columnValue + "   rowID: " + rowID);
			if (columnName == "column1" && columnValue == "test")
			{
				setUpdateColumnValue(updateRowsMap, rowID, columnName, "updatedTestValue");
			}
			if (columnName == "column1" && columnValue == "999")
			{
				setUpdateColumnValue(updateRowsMap, rowID, columnName, "updated999Value");
			}
		}
		if (!updateRowsMap.isEmpty())
		{
			updateAppSpecificTableInfors(tableName, capIDModel, updateRowsMap);
		}
	}	
}



/**
* Set update column value. format: Map<rowID, Map<columnName, columnValue>>
**/
function setUpdateColumnValue(updateRowsMap/** Map<rowID, Map<columnName, columnValue>> **/, rowID, columnName, columnValue)
{
	var updateFieldsMap = updateRowsMap.get(rowID);
	if (updateFieldsMap == null)
	{
		updateFieldsMap = aa.util.newHashMap();
		updateRowsMap.put(rowID, updateFieldsMap);
	}
	updateFieldsMap.put(columnName, columnValue);
}

/**
* update ASIT rows data. updateRowsMap format: Map<rowID, Map<columnName, columnValue>>
**/
function updateAppSpecificTableInfors(tableName, capIDModel, updateRowsMap/** Map<rowID, Map<columnName, columnValue>> **/)
{
	if (updateRowsMap == null || updateRowsMap.isEmpty())
	{
		return;
	}
	
	var asitTableScriptModel = aa.appSpecificTableScript.createTableScriptModel();
	var asitTableModel = asitTableScriptModel.getTabelModel();
	var rowList = asitTableModel.getRows();
	asitTableModel.setSubGroup(tableName);
	var rowIdArray = updateRowsMap.keySet().toArray();
	for (var i = 0; i < rowIdArray.length; i++)
	{
		var rowScriptModel = aa.appSpecificTableScript.createRowScriptModel();
		var rowModel = rowScriptModel.getRow();
		rowModel.setFields(updateRowsMap.get(rowIdArray[i]));
		rowModel.setId(rowIdArray[i]);
		rowList.add(rowModel);
	}
	return aa.appSpecificTableScript.updateAppSpecificTableInfors(capIDModel, asitTableModel);
}
aa.print("success");
