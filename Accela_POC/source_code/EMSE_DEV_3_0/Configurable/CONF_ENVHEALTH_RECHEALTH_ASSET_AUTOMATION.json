{"EnvHealth/Rec Health/Pool/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Pool Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": false, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Rec Health/Spa/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Spa Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": false, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}}