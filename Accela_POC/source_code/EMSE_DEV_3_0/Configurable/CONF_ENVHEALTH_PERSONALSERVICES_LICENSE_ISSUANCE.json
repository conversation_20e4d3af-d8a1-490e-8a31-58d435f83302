{"EnvHealth/Personal Services/*/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Issues a Personal Services License", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"parentLicense": "EnvHealth/Personal Services/*/Permit", "issuedStatus": "Active", "copyCustomFields": ["ALL"], "copyCustomTables": ["ALL"], "expirationType": "Expiration Code", "expirationPeriod": "EH_GENERAL", "copyContacts": ["ALL"], "createLP": false, "licenseTable": "", "refLPType": "Business", "contactType": "Applicant", "contactAddressType": "Mailing"}, "postScript": ""}]}}