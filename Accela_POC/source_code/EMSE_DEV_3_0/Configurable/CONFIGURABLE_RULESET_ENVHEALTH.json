{"WorkflowTaskUpdateBefore": {"StandardScripts": ["STDBASE_RECORD_AUTOMATION", "_STDBASE_RECORD_VALIDATION"]}, "WorkflowTaskUpdateAfter": {"StandardScripts": ["STDBASE_RECORD_AUTOMATION", "_STDBASE_SEND_CONTACT_EMAILS", "STDBASE_ASSET_AUTOMATION", "STDBASE_TIME_ACCOUNTING_AUTOMATION"]}, "Extraneous": {"StandardScripts": ["STDBASE_RECORD_AUTOMATION", "STDBASE_LICENSE_RENEWAL_ISSUANCE", "STDBASE_SEND_CONTACT_EMAILS", "STDBASE_UPDATE_FIELDS", "STDBASE_COPY_RECORD_DATA", "STDBASE_ASSET_AUTOMATION", "STDBASE_TIME_ACCOUNTING_AUTOMATION", "STDBASE_INSPECTION_AUTOMATION"]}, "ConvertToRealCAPAfter": {"StandardScripts": ["STDBASE_SEND_CONTACT_EMAILS", "STDBASE_PEOPLE_AUTOMATION"]}, "InvoiceFeeAfter": {"StandardScripts": ["STDBASE_SEND_CONTACT_EMAILS"]}, "ApplicationSubmitAfter": {"StandardScripts": ["STDBASE_PEOPLE_AUTOMATION", "STDBASE_RECORD_ADDRESS_AUTOMATION", "STDBASE_COPY_RECORD_DATA", "STDBASE_CONDITION_DOCUMENTS"]}, "InspectionResultModifyAfter": {"StandardScripts": ["STDBASE_TIME_ACCOUNTING_AUTOMATION", "STDBASE_INSPECTION_AUTOMATION", "STDBASE_SEND_CONTACT_EMAILS"]}, "InspectionResultSubmitAfter": {"StandardScripts": ["STDBASE_TIME_ACCOUNTING_AUTOMATION", "STDBASE_INSPECTION_AUTOMATION", "STDBASE_SEND_CONTACT_EMAILS"]}}