{"EnvHealth/Food Retail/*/Renewal": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Updates parent EnvHealth record", "operators": {}}, "criteria": {"task": ["<PERSON><PERSON>"], "status": ["Renewed"]}, "preScript": "", "action": {"issuedRecordStatus": "Active", "issuedExpirationStatus": "Active", "issuedLPStatus": "A", "expirationType": "Expiration Code", "expirationPeriod": "", "customExpirationFunction": "", "copyComponents": []}, "postScript": ""}]}}