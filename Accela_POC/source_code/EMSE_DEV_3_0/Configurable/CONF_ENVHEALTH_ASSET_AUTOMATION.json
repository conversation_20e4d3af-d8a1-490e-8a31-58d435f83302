{"EnvHealth/Food Retail/Bakery/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Food Facility/*": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Restaurant/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Institutions/Child Day Care/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food/Retail/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Bar/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Cafeteria/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Commissary/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Elderly Nutrition Catered/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Farmers Market/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Food Catering/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Grocery/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Hospital Food/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Hotel Food/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Meat Market/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Shared Kitchen/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Snack Bar/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Institutions/Adult Day Care/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Institutions/Care/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Institutions/Correctional Facility/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Institutions/Educational Building/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Institutions/Educational Building Care/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Institutions/Nursing Home/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Institutions/Residential Care/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Personal Services/Artificial Tanning/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Personal Services/Body Art/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Personal Services/Body Piercing/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Personal Services/Massage Parlor/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "Pending", "copyContacts": ["Business Owner", "Property Manager"], "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}}