{"_EnvHealth/Food Retail/Bakery/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create <PERSON><PERSON>", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Bar/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Bar <PERSON>", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Cafeteria/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Cafeteria Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Commissary/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Commissary <PERSON><PERSON>", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Elderly Nutrition Catered/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Elderly Nutrition Catered Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Farmers Market/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Farmers Market Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Food Catering/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Catering Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Food Facility/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Facility Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Food Stand/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Food Stand Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Grocery/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Grocery Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Hospital Food/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Hospital Food Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Hotel Food/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Hotel Food Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Limited Food/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Limited Food Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Meat Market/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Meat Market Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Mobile Food/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Mobile Food Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Pushcart Food/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Pushcart Food Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Restaurant/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Restaurant Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Shared Kitchen/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Shared Kitchen Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "_EnvHealth/Food Retail/Snack Bar/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Snack Bar Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Temporary Food Establishment/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Temporary Food Establishment Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Vending Machine/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Vending Machine Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Caterer/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Vending Machine Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Restricted Food Service/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Vending Machine Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Temporary Event/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Vending Machine Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}, "EnvHealth/Food Retail/Temporary Event Vendor/Application": {"WorkflowTaskUpdateAfter": [{"metadata": {"description": "Create Vending Machine Asset", "operators": {}}, "preScript": "", "criteria": {"task": ["Permit Issuance"], "status": ["Issued"]}, "action": {"copyContacts": ["Facility Owner"], "updateExistingRefAsset": true, "assetType": "Building", "assetGroup": "Facilities", "checkExistingAsset": true, "dateOfService": "", "status": "In Service", "copyAddress": ["ALL"], "customFieldsDefaultMapping": {}, "linkParent": true, "copyDocumentTypes": ["Site Plan", "Building Plan"], "copyAppNameToAssetName": true}, "postScript": ""}]}}