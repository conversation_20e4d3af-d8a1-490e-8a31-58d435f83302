//showDebug = true;
//logDebug("publicUser:"+publicUser);
if(publicUser)
{	
	var emailTo = "";
	if(appMatch("EnvHealth/Amendment/*/*",capId))			emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Enforcement/*/*",capId))			emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Food Retail/*/*",capId))			emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Food/*/*",capId))				emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Hazmat/*/*",capId))				emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Housing/*/*",capId))				emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Institutions/*/*",capId))		emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Land Use/*/*",capId))			emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Personal Services/*/*",capId))	emailTo = "<EMAIL>";
	if(appMatch("EnvHealth/Rec Health/*/*",capId))			emailTo = "<EMAIL>";

	var emailSender 	= "<EMAIL>"
	var emailTemplate 	= "APP_SUBMITTED_ACA";
	var eParams = aa.util.newHashtable(); 
	var rFiles = [];
	var capIDScriptModel = aa.cap.createCapIDScriptModel(capId.getID1(),capId.getID2(),capId.getID3());

	var myCap = aa.cap.getCap(capId).getOutput();
	
	addParameter(eParams, "$$altID$$", capId.getCustomID());
	addParameter(eParams, "$$status$$", capStatus);
	//addParameter(eParams, "$$balanceDue$$", feeTotal);
	addParameter(eParams, "$$recordAlias$$", myCap.getCapType().getAlias());
	if(emailTo!="")	aa.document.sendEmailAndSaveAsDocument(emailSender, emailTo, "", emailTemplate, eParams, capIDScriptModel, rFiles);
		
}