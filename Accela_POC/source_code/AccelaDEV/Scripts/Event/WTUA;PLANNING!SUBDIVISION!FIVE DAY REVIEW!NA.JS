
if (wfTask == 'Task Assignment' && (wfStatus == 'Assign Electronic Review' || wfStatus == 'Assign Paper Review')) {

	//start replaced branch: ES_SUB_FIVE_DAY_ASSIGN_NOTIFICATIONS
	{
		var TSI = new Array();
		var emailTo = '<EMAIL>';
		loadTaskSpecific(TSI, capId);
		if (AInfo['Land Development Review'] == 'Yes') {
			email('<EMAIL>;<EMAIL>', '<EMAIL>', 'Land Development Review is Active', 'Please review task Land Development Review of Record Number ' + capIDString);
		}

		if (AInfo['EPWU - Storm Water Review'] == 'Yes') {
			email('<EMAIL>;<EMAIL>;<EMAIL>', '<EMAIL>', 'EPWU - Storm Water Review is Active', 'Please review task EPWU - Storm Water Review of Record Number ' + capIDString);
		}

		if (AInfo['EPWU - PSB Review'] == 'Yes') {
			email('<EMAIL>;<EMAIL>;	<EMAIL>', '<EMAIL>', 'EPWU - Storm Water Review is Active', 'Please review task EPWU - Storm Water Review of Record Number ' + capIDString);
		}

		if (AInfo['Parks Review'] == 'Yes') {
			email('<EMAIL>;<EMAIL>;<EMAIL>', '<EMAIL>', 'Parks Review is Active', 'Please review task Parks Review of Record Number ' + capIDString);
		}

		if (AInfo['911 Review'] == 'Yes') {
			email('<EMAIL>;<EMAIL>', '<EMAIL>', '911 Review is Active', 'Please review task 911 Review of Record Number ' + capIDString);
		}

	}
	//end replaced branch: ES_SUB_FIVE_DAY_ASSIGN_NOTIFICATIONS;
}

if (wfTask == 'Application Submittal' && wfStatus == 'Assigned') {

	//replaced branch(ES_WORKFLOW_CASE_EMAIL_NOTIFICATION)
	ES_WORKFLOW_CASE_EMAIL_NOTIFICATION();
}
